import 'package:my_video/app_imports.dart';

class InitialSetupPage extends StatefulWidget {
  const InitialSetupPage({super.key});

  @override
  State<InitialSetupPage> createState() => _InitialSetupPageState();
}

class _InitialSetupPageState extends State<InitialSetupPage> {
  final MovieRepository _movieRepository = GetIt.instance<MovieRepository>();

  bool _isLoading = true;
  List<FilterLanguage> _availableLanguages = [];
  List<FilterGenre> _availableGenres = [];

  List<FilterLanguage> _selectedLanguages = [];
  List<FilterGenre> _selectedGenres = [];

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      // Load available filters from API
      final filterResponse = await _movieRepository.getFilters();

      setState(() {
        _availableLanguages = filterResponse.languages;
        _availableGenres = filterResponse.genres;
      });

      // Set default selections (English + Bollywood)
      final defaultFilters = UserPreferenceService.getDefaultPreferences();
      setState(() {
        _selectedLanguages = List.from(defaultFilters.selectedLanguages);
        _selectedGenres = List.from(defaultFilters.selectedGenres);
      });
    } catch (e) {
      AppHelper.logDebug('Error loading filters: $e');
      // Set default values if API fails
      setState(() {
        _availableLanguages = [
          FilterLanguage(langId: 1, language: 'English'),
          FilterLanguage(langId: 2, language: 'हिंदी'),
          FilterLanguage(langId: 3, language: 'ગુજરાતી'),
          FilterLanguage(langId: 5, language: 'தமிழ்'),
          FilterLanguage(langId: 7, language: 'മലയാളം'),
        ];
        _availableGenres = [
          FilterGenre(genreId: 3, genre: 'Bollywood'),
          FilterGenre(genreId: 4, genre: 'Hollywood'),
          FilterGenre(genreId: 2, genre: 'Tollywood'),
          FilterGenre(genreId: 1, genre: 'Mollywood'),
        ];

        // Set default selections
        _selectedLanguages = [FilterLanguage(langId: 1, language: 'English')];
        _selectedGenres = [FilterGenre(genreId: 3, genre: 'Bollywood')];
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _toggleLanguage(FilterLanguage language) {
    setState(() {
      if (_selectedLanguages.contains(language)) {
        _selectedLanguages.remove(language);
      } else {
        _selectedLanguages.add(language);
      }
    });
  }

  void _toggleGenre(FilterGenre genre) {
    setState(() {
      if (_selectedGenres.contains(genre)) {
        _selectedGenres.remove(genre);
      } else {
        _selectedGenres.add(genre);
      }
    });
  }

  Future<void> _completeSetup() async {
    try {
      // Ensure at least one language and genre is selected
      if (_selectedLanguages.isEmpty) {
        AppHelper.showToast(
          'Please select at least one language',
          isError: true,
        );
        return;
      }

      if (_selectedGenres.isEmpty) {
        AppHelper.showToast('Please select at least one genre', isError: true);
        return;
      }

      // Save preferences
      final selectedFilters = SelectedFilters(
        selectedLanguages: _selectedLanguages,
        selectedGenres: _selectedGenres,
        selectedCategories: [],
      );

      await UserPreferenceService.instance.saveSelectedFilters(selectedFilters);
      await UserPreferenceService.instance.setFirstLaunchCompleted();

      // Navigate to main app
      if (mounted) {
        context.go(AppRoutes.mainNavigation);
      }
    } catch (e) {
      AppHelper.logDebug('Error completing setup: $e');
      AppHelper.showToast('Failed to save preferences', isError: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Initialize MySize for responsive design
    MySize.init(context);

    return AppScaffold(
      backgroundColor: AppColorConstants.backgroundColor,
      body: SafeArea(
        child: _isLoading ? _buildLoadingView() : _buildSetupView(),
      ),
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppColorConstants.primaryColor),
          Space.height(16),
          AppText(
            text: 'Setting up your preferences...',
            fontSize: 16,
            color: AppColorConstants.textSecondary,
          ),
        ],
      ),
    );
  }

  Widget _buildSetupView() {
    return Column(
      children: [
        // Header
        Container(
          padding: EdgeInsets.all(MySize.width(24)),
          child: Column(
            children: [
              Icon(
                Icons.movie_filter,
                size: MySize.width(64),
                color: AppColorConstants.primaryColor,
              ),
              Space.height(16),
              AppText(
                text: 'Welcome to My Video!',
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: AppColorConstants.colorWhite,
                textAlign: TextAlign.center,
              ),
              Space.height(8),
              AppText(
                text:
                    'Let\'s personalize your experience by selecting your preferred languages and genres.',
                fontSize: 16,
                color: AppColorConstants.textSecondary,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),

        // Content
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: MySize.width(24)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Languages Section
                _buildSectionTitle('Select Languages'),
                Space.height(12),
                AppText(
                  text: 'Choose the languages you prefer for movies and shows',
                  fontSize: 14,
                  color: AppColorConstants.textHint,
                ),
                Space.height(16),
                _buildLanguageChips(),
                Space.height(32),

                // Genres Section
                _buildSectionTitle('Select Genres'),
                Space.height(12),
                AppText(
                  text: 'Pick your favorite movie genres',
                  fontSize: 14,
                  color: AppColorConstants.textHint,
                ),
                Space.height(16),
                _buildGenreChips(),
                Space.height(32),
              ],
            ),
          ),
        ),

        // Continue Button
        Container(
          padding: EdgeInsets.all(MySize.width(24)),
          child: Column(
            children: [
              SizedBox(
                width: double.infinity,
                height: MySize.height(48),
                child: AppButton(
                  text: 'Continue',
                  onPressed: _completeSetup,
                  backgroundColor: AppColorConstants.primaryColor,
                  textColor: AppColorConstants.colorWhite,
                  borderRadius: MySize.radius(8),
                ),
              ),
              Space.height(12),
              AppText(
                text: 'You can change these preferences anytime in settings',
                fontSize: 12,
                color: AppColorConstants.textHint,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return AppText(
      text: title,
      fontSize: 20,
      fontWeight: FontWeight.w600,
      color: AppColorConstants.colorWhite,
    );
  }

  Widget _buildLanguageChips() {
    return Wrap(
      spacing: MySize.width(8),
      runSpacing: MySize.height(8),
      children: _availableLanguages.map((language) {
        final isSelected = _selectedLanguages.contains(language);
        return GestureDetector(
          onTap: () => _toggleLanguage(language),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: MySize.width(16),
              vertical: MySize.height(10),
            ),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColorConstants.primaryColor
                  : AppColorConstants.cardColor,
              borderRadius: BorderRadius.circular(MySize.width(20)),
              border: Border.all(
                color: isSelected
                    ? AppColorConstants.primaryColor
                    : AppColorConstants.colorGrey,
                width: 1,
              ),
            ),
            child: AppText(
              text: language.language,
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              color: isSelected
                  ? AppColorConstants.colorWhite
                  : AppColorConstants.colorGrey,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildGenreChips() {
    return Wrap(
      spacing: MySize.width(8),
      runSpacing: MySize.height(8),
      children: _availableGenres.map((genre) {
        final isSelected = _selectedGenres.contains(genre);
        return GestureDetector(
          onTap: () => _toggleGenre(genre),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: MySize.width(16),
              vertical: MySize.height(10),
            ),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColorConstants.primaryColor
                  : AppColorConstants.cardColor,
              borderRadius: BorderRadius.circular(MySize.width(20)),
              border: Border.all(
                color: isSelected
                    ? AppColorConstants.primaryColor
                    : AppColorConstants.colorGrey,
                width: 1,
              ),
            ),
            child: AppText(
              text: genre.genre,
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              color: isSelected
                  ? AppColorConstants.colorWhite
                  : AppColorConstants.colorGrey,
            ),
          ),
        );
      }).toList(),
    );
  }
}
